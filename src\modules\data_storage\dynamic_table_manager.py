"""
月度工资异动处理系统 - 动态表管理器

本模块实现动态表管理功能：
- 工资数据表的动态创建
- 表结构的版本管理
- 字段映射和验证
- 表结构变更处理
- 索引自动管理

主要功能：
- create_salary_table(): 创建工资数据表
- update_table_schema(): 更新表结构
- manage_table_indexes(): 管理表索引
- validate_table_structure(): 验证表结构
- get_table_columns(): 获取表字段信息

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
作者: 开发团队
"""

import json
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union, Set
from datetime import datetime
from dataclasses import dataclass
import pandas as pd

# 导入项目内部模块
from src.utils.log_config import setup_logger
from src.modules.system_config import ConfigManager
from .database_manager import DatabaseManager, get_database_manager

# 初始化日志
logger = setup_logger("data_storage.dynamic_table_manager")


@dataclass
class ColumnDefinition:
    """列定义信息"""
    name: str
    type: str  # TEXT, INTEGER, REAL, BLOB
    nullable: bool = True
    default: Optional[str] = None
    unique: bool = False
    description: Optional[str] = None


@dataclass
class TableSchema:
    """表结构定义"""
    table_name: str
    columns: List[ColumnDefinition]
    primary_key: List[str]
    indexes: List[Dict[str, Any]]
    version: int = 1
    description: Optional[str] = None


class DynamicTableManager:
    """动态表管理器，负责表结构的动态创建和管理"""
    
    def __init__(self, 
                 db_manager: Optional[DatabaseManager] = None,
                 config_manager: Optional[ConfigManager] = None):
        """
        初始化动态表管理器
        
        Args:
            db_manager: 数据库管理器实例
            config_manager: 配置管理器实例
        """
        self.logger = setup_logger("data_storage.dynamic_table_manager")
        
        # 获取管理器实例
        self.db_manager = db_manager or get_database_manager()
        self.config_manager = config_manager or ConfigManager()
        
        # 预定义的表结构模板
        self._table_templates = self._initialize_table_templates()
        
        # 支持的数据类型映射
        self._type_mapping = {
            'string': 'TEXT',
            'str': 'TEXT',
            'text': 'TEXT',
            'varchar': 'TEXT',
            'int': 'INTEGER',
            'integer': 'INTEGER',
            'number': 'INTEGER',
            'float': 'REAL',
            'real': 'REAL',
            'decimal': 'REAL',
            'bool': 'INTEGER',
            'boolean': 'INTEGER',
            'date': 'TEXT',
            'datetime': 'TEXT',
            'timestamp': 'TEXT'
        }
        
        self.logger.info("动态表管理器初始化完成")
    
    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在

        Args:
            table_name (str): 要检查的表名

        Returns:
            bool: 如果表存在则返回 True，否则返回 False
        """
        try:
            return self.db_manager.table_exists(table_name)
        except Exception as e:
            self.logger.error(f"检查表 {table_name} 是否存在时出错: {e}")
            return False
    
    def _initialize_table_templates(self) -> Dict[str, TableSchema]:
        """初始化预定义的表结构模板"""
        templates = {}
        
        # 工资数据表模板
        salary_columns = [
            ColumnDefinition("id", "INTEGER", False, None, True, "自增主键"),
            ColumnDefinition("employee_id", "TEXT", False, None, False, "员工工号"),
            ColumnDefinition("employee_name", "TEXT", False, None, False, "员工姓名"),
            ColumnDefinition("id_card", "TEXT", True, None, False, "身份证号"),
            ColumnDefinition("department", "TEXT", True, None, False, "部门"),
            ColumnDefinition("position", "TEXT", True, None, False, "职位"),
            ColumnDefinition("basic_salary", "REAL", True, "0", False, "基本工资"),
            ColumnDefinition("performance_bonus", "REAL", True, "0", False, "绩效奖金"),
            ColumnDefinition("overtime_pay", "REAL", True, "0", False, "加班费"),
            ColumnDefinition("allowance", "REAL", True, "0", False, "津贴"),
            ColumnDefinition("deduction", "REAL", True, "0", False, "扣款"),
            ColumnDefinition("total_salary", "REAL", True, "0", False, "总工资"),
            ColumnDefinition("month", "TEXT", False, None, False, "月份"),
            ColumnDefinition("year", "INTEGER", False, None, False, "年份"),
            ColumnDefinition("created_at", "TEXT", False, None, False, "创建时间"),
            ColumnDefinition("updated_at", "TEXT", False, None, False, "更新时间")
        ]
        
        templates["salary_data"] = TableSchema(
            table_name="salary_data",
            columns=salary_columns,
            primary_key=["id"],
            indexes=[
                {"name": "idx_employee_id", "columns": ["employee_id"], "unique": False},
                {"name": "idx_month_year", "columns": ["month", "year"], "unique": False},
                {"name": "idx_employee_month", "columns": ["employee_id", "month", "year"], "unique": True}
            ],
            description="工资数据表"
        )
        
        # 异动记录表模板
        change_columns = [
            ColumnDefinition("id", "INTEGER", False, None, True, "自增主键"),
            ColumnDefinition("employee_id", "TEXT", False, None, False, "员工工号"),
            ColumnDefinition("change_type", "TEXT", False, None, False, "异动类型"),
            ColumnDefinition("field_name", "TEXT", False, None, False, "变化字段"),
            ColumnDefinition("old_value", "TEXT", True, None, False, "旧值"),
            ColumnDefinition("new_value", "TEXT", True, None, False, "新值"),
            ColumnDefinition("change_amount", "REAL", True, "0", False, "变化金额"),
            ColumnDefinition("change_reason", "TEXT", True, None, False, "变化原因"),
            ColumnDefinition("month", "TEXT", False, None, False, "月份"),
            ColumnDefinition("year", "INTEGER", False, None, False, "年份"),
            ColumnDefinition("detected_at", "TEXT", False, None, False, "检测时间"),
            ColumnDefinition("verified", "INTEGER", False, "0", False, "是否验证")
        ]
        
        templates["salary_changes"] = TableSchema(
            table_name="salary_changes",
            columns=change_columns,
            primary_key=["id"],
            indexes=[
                {"name": "idx_change_employee", "columns": ["employee_id"], "unique": False},
                {"name": "idx_change_type", "columns": ["change_type"], "unique": False},
                {"name": "idx_change_month", "columns": ["month", "year"], "unique": False}
            ],
            description="工资异动记录表"
        )
        
        # 杂项数据表模板（用于未分类的工资数据）
        misc_data_columns = [
            ColumnDefinition("id", "INTEGER", False, None, True, "自增主键"),
            ColumnDefinition("employee_id", "TEXT", False, None, False, "员工工号"),
            ColumnDefinition("employee_name", "TEXT", False, None, False, "员工姓名"),
            ColumnDefinition("id_card", "TEXT", True, None, False, "身份证号"),
            ColumnDefinition("department", "TEXT", True, None, False, "部门"),
            ColumnDefinition("position", "TEXT", True, None, False, "职位"),
            ColumnDefinition("basic_salary", "REAL", True, "0", False, "基本工资"),
            ColumnDefinition("performance_bonus", "REAL", True, "0", False, "绩效奖金"),
            ColumnDefinition("overtime_pay", "REAL", True, "0", False, "加班费"),
            ColumnDefinition("allowance", "REAL", True, "0", False, "津贴"),
            ColumnDefinition("deduction", "REAL", True, "0", False, "扣款"),
            ColumnDefinition("total_salary", "REAL", True, "0", False, "总工资"),
            ColumnDefinition("data_source", "TEXT", True, None, False, "数据来源"),
            ColumnDefinition("import_time", "TEXT", True, None, False, "导入时间"),
            ColumnDefinition("month", "TEXT", False, None, False, "月份"),
            ColumnDefinition("year", "INTEGER", False, None, False, "年份"),
            ColumnDefinition("created_at", "TEXT", False, None, False, "创建时间"),
            ColumnDefinition("updated_at", "TEXT", False, None, False, "更新时间")
        ]
        
        templates["misc_data"] = TableSchema(
            table_name="misc_data",
            columns=misc_data_columns,
            primary_key=["id"],
            indexes=[
                {"name": "idx_misc_employee_id", "columns": ["employee_id"], "unique": False},
                {"name": "idx_misc_month_year", "columns": ["month", "year"], "unique": False},
                {"name": "idx_misc_data_source", "columns": ["data_source"], "unique": False},
                {"name": "idx_misc_employee_month", "columns": ["employee_id", "month", "year"], "unique": False}
            ],
            description="杂项工资数据表"
        )

        return templates
    
    def create_salary_table(self, 
                           month: str, 
                           year: int, 
                           custom_columns: Optional[List[ColumnDefinition]] = None) -> bool:
        """
        创建工资数据表
        
        Args:
            month: 月份 (格式: YYYY-MM)
            year: 年份
            custom_columns: 自定义列定义，如果提供则合并到默认列中
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 生成表名
            table_name = f"salary_data_{year}_{month.replace('-', '_')}"
            
            # 检查表是否已存在
            if self.db_manager.table_exists(table_name):
                self.logger.info(f"表 {table_name} 已存在")
                return True
            
            # 获取基础表结构
            base_schema = self._table_templates["salary_data"]
            columns = base_schema.columns.copy()
            
            # 合并自定义列
            if custom_columns:
                # 检查列名冲突
                existing_names = {col.name for col in columns}
                for custom_col in custom_columns:
                    if custom_col.name not in existing_names:
                        columns.append(custom_col)
                        self.logger.debug(f"添加自定义列: {custom_col.name}")
                    else:
                        self.logger.warning(f"列名冲突，跳过自定义列: {custom_col.name}")
            
            # 创建表结构
            schema = TableSchema(
                table_name=table_name,
                columns=columns,
                primary_key=base_schema.primary_key,
                indexes=base_schema.indexes.copy(),
                description=f"{year}年{month}月工资数据表"
            )
            
            # 执行创建
            success = self._create_table_from_schema(schema)
            
            if success:
                # 记录表元信息
                self._record_table_metadata(schema)
                self.logger.info(f"工资数据表创建成功: {table_name}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"创建工资数据表失败: {str(e)}")
            return False
    
    def _create_table_from_schema(self, schema: TableSchema) -> bool:
        """
        根据表结构创建表
        
        Args:
            schema: 表结构定义
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 构建CREATE TABLE语句
            sql_parts = [f"CREATE TABLE {self._quote_identifier(schema.table_name)} ("]
            
            # 添加列定义
            column_definitions = []
            for col in schema.columns:
                # 使用引号包围列名，确保中文和特殊字符列名能正确处理
                col_def = f"{self._quote_identifier(col.name)} {col.type}"
                
                if not col.nullable:
                    col_def += " NOT NULL"
                
                if col.default is not None:
                    col_def += f" DEFAULT {col.default}"
                
                if col.unique:
                    col_def += " UNIQUE"
                
                column_definitions.append(col_def)
            
            # 添加主键约束
            if schema.primary_key:
                pk_columns = [self._quote_identifier(col) for col in schema.primary_key]
                pk_constraint = f"PRIMARY KEY ({', '.join(pk_columns)})"
                column_definitions.append(pk_constraint)
            
            sql_parts.append(",\n    ".join(column_definitions))
            sql_parts.append(")")
            
            create_sql = "\n".join(sql_parts)
            
            # 记录生成的SQL语句，便于调试
            self.logger.debug(f"生成的CREATE TABLE语句: {create_sql}")
            
            # 执行创建表语句
            self.db_manager.execute_update(create_sql)
            
            # 创建索引
            for index in schema.indexes:
                self._create_index(schema.table_name, index)
            
            return True
            
        except Exception as e:
            self.logger.error(f"创建表失败: {str(e)}")
            return False
    
    def _quote_identifier(self, identifier: str) -> str:
        """
        为SQL标识符添加引号，确保中文和特殊字符能正确处理
        
        Args:
            identifier: 标识符（表名、列名等）
            
        Returns:
            str: 带引号的标识符
        """
        # SQLite使用双引号包围标识符
        # 如果标识符中包含双引号，需要转义
        escaped = identifier.replace('"', '""')
        return f'"{escaped}"'
    
    def _create_index(self, table_name: str, index_def: Dict[str, Any]) -> bool:
        """
        创建索引
        
        Args:
            table_name: 表名
            index_def: 索引定义
            
        Returns:
            bool: 是否创建成功
        """
        try:
            index_name = index_def["name"]
            columns = index_def["columns"]
            unique = index_def.get("unique", False)
            
            unique_keyword = "UNIQUE " if unique else ""
            # 为列名添加引号
            quoted_columns = [self._quote_identifier(col) for col in columns]
            columns_str = ", ".join(quoted_columns)
            
            # 使用 IF NOT EXISTS 增加健壮性
            sql = f"CREATE {unique_keyword}INDEX IF NOT EXISTS {self._quote_identifier(index_name)} ON {self._quote_identifier(table_name)} ({columns_str})"
            
            self.db_manager.execute_update(sql)
            self.logger.debug(f"索引创建成功: {index_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建索引失败: {str(e)}")
            return False
    
    def _record_table_metadata(self, schema: TableSchema) -> None:
        """
        记录表元信息
        
        Args:
            schema: 表结构定义
        """
        try:
            # 生成表结构哈希值
            schema_hash = self._calculate_schema_hash(schema)
            
            metadata_sql = """
                INSERT INTO table_metadata 
                (table_name, table_type, schema_version, description, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """
            
            current_time = datetime.now().isoformat()
            
            params = (
                schema.table_name,
                "salary_data" if "salary_data" in schema.table_name else "unknown",
                schema.version,
                schema.description,
                current_time,
                current_time
            )
            
            self.db_manager.execute_update(metadata_sql, params)
            
            # 保存详细的表结构信息到系统配置
            schema_key = f"table_schema_{schema.table_name}"
            schema_json = self._schema_to_json(schema)
            
            config_sql = """
                INSERT OR REPLACE INTO system_config (key, value, description, updated_at)
                VALUES (?, ?, ?, ?)
            """
            
            config_params = (
                schema_key,
                schema_json,
                f"表结构信息: {schema.table_name}",
                current_time
            )
            
            self.db_manager.execute_update(config_sql, config_params)
            
        except Exception as e:
            self.logger.error(f"记录表元信息失败: {str(e)}")
    
    def _calculate_schema_hash(self, schema: TableSchema) -> str:
        """
        计算表结构哈希值
        
        Args:
            schema: 表结构定义
            
        Returns:
            str: 哈希值
        """
        schema_str = self._schema_to_json(schema)
        return hashlib.md5(schema_str.encode('utf-8')).hexdigest()
    
    def _schema_to_json(self, schema: TableSchema) -> str:
        """
        将表结构转换为JSON字符串
        
        Args:
            schema: 表结构定义
            
        Returns:
            str: JSON字符串
        """
        schema_dict = {
            "table_name": schema.table_name,
            "columns": [
                {
                    "name": col.name,
                    "type": col.type,
                    "nullable": col.nullable,
                    "default": col.default,
                    "unique": col.unique,
                    "description": col.description
                }
                for col in schema.columns
            ],
            "primary_key": schema.primary_key,
            "indexes": schema.indexes,
            "version": schema.version,
            "description": schema.description
        }
        
        return json.dumps(schema_dict, ensure_ascii=False, sort_keys=True)
    
    def update_table_schema(self, 
                           table_name: str, 
                           new_columns: List[ColumnDefinition],
                           drop_columns: Optional[List[str]] = None) -> bool:
        """
        更新表结构
        
        Args:
            table_name: 表名
            new_columns: 新增列定义
            drop_columns: 要删除的列名列表
            
        Returns:
            bool: 是否更新成功
        """
        try:
            if not self.db_manager.table_exists(table_name):
                self.logger.error(f"表不存在: {table_name}")
                return False
            
            # SQLite不支持直接删除列，需要重建表
            if drop_columns:
                return self._rebuild_table_with_schema_change(table_name, new_columns, drop_columns)
            
            # 只添加新列的情况
            for col in new_columns:
                alter_sql = f"ALTER TABLE {table_name} ADD COLUMN {col.name} {col.type}"
                
                if not col.nullable:
                    if col.default is not None:
                        alter_sql += f" NOT NULL DEFAULT {col.default}"
                    else:
                        self.logger.warning(f"新增非空列 {col.name} 但没有默认值，将设为可空")
                
                self.db_manager.execute_update(alter_sql)
                self.logger.info(f"新增列成功: {table_name}.{col.name}")
            
            # 更新表元信息
            self._update_table_metadata(table_name)
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新表结构失败: {str(e)}")
            return False
    
    def _rebuild_table_with_schema_change(self, 
                                        table_name: str,
                                        new_columns: List[ColumnDefinition],
                                        drop_columns: List[str]) -> bool:
        """
        重建表以应用结构变更（SQLite限制）
        
        Args:
            table_name: 表名
            new_columns: 新增列
            drop_columns: 删除列
            
        Returns:
            bool: 是否成功
        """
        try:
            # 获取当前表结构
            current_columns = self.get_table_columns(table_name)
            if not current_columns:
                return False
            
            # 创建新的列定义
            new_column_defs = []
            
            # 保留现有列（除了要删除的）
            for col_info in current_columns:
                if col_info["name"] not in drop_columns:
                    col_def = ColumnDefinition(
                        name=col_info["name"],
                        type=col_info["type"],
                        nullable=not col_info["notnull"],
                        default=col_info["dflt_value"]
                    )
                    new_column_defs.append(col_def)
            
            # 添加新列
            new_column_defs.extend(new_columns)
            
            # 创建临时表
            temp_table = f"{table_name}_temp_{int(datetime.now().timestamp())}"
            temp_schema = TableSchema(
                table_name=temp_table,
                columns=new_column_defs,
                primary_key=["id"],  # 简化处理，使用默认主键
                indexes=[],
                description=f"临时表: {table_name}"
            )
            
            # 创建临时表
            if not self._create_table_from_schema(temp_schema):
                return False
            
            # 迁移数据
            keep_columns = [col.name for col in new_column_defs 
                          if col.name not in [nc.name for nc in new_columns]]
            
            if keep_columns:
                columns_str = ", ".join(keep_columns)
                copy_sql = f"INSERT INTO {temp_table} ({columns_str}) SELECT {columns_str} FROM {table_name}"
                self.db_manager.execute_update(copy_sql)
            
            # 删除原表
            self.db_manager.execute_update(f"DROP TABLE {table_name}")
            
            # 重命名临时表
            self.db_manager.execute_update(f"ALTER TABLE {temp_table} RENAME TO {table_name}")
            
            self.logger.info(f"表结构重建成功: {table_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"重建表失败: {str(e)}")
            return False
    
    def get_table_columns(self, table_name: str) -> List[Dict[str, Any]]:
        """
        获取表的列信息
        
        Args:
            table_name: 表名
            
        Returns:
            List[Dict[str, Any]]: 列信息列表
        """
        try:
            return self.db_manager.get_table_info(table_name)
        except Exception as e:
            self.logger.error(f"获取表 {table_name} 的列信息失败: {e}")
            return []
    
    def validate_table_structure(self, table_name: str, expected_columns: List[str]) -> bool:
        """
        验证表结构是否符合预期
        
        Args:
            table_name: 表名
            expected_columns: 期望的列名列表
            
        Returns:
            bool: 是否符合预期
        """
        try:
            actual_columns = self.get_table_columns(table_name)
            actual_names = {col["name"] for col in actual_columns}
            expected_names = set(expected_columns)
            
            missing = expected_names - actual_names
            extra = actual_names - expected_names
            
            if missing:
                self.logger.warning(f"表 {table_name} 缺少列: {missing}")
            
            if extra:
                self.logger.info(f"表 {table_name} 额外列: {extra}")
            
            return len(missing) == 0
            
        except Exception as e:
            self.logger.error(f"验证表结构失败: {str(e)}")
            return False
    
    def _update_table_metadata(self, table_name: str) -> None:
        """更新指定表的元数据"""
        self.logger.warning(f"更新表 {table_name} 的元数据功能尚未完全实现")

    def get_table_list(self, table_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取指定类型的数据表列表
        现在包含数据库同步检查和重试机制，确保WAL模式数据完全同步
        
        Args:
            table_type: 表类型前缀，如 'salary_data', 'misc_data' 等
            
        Returns:
            表信息列表
        """
        max_retries = 3
        retry_delay = 0.1  # 100ms
        
        for attempt in range(max_retries):
            try:
                # Step 1: 确保数据库同步
                self._ensure_database_sync()
                
                # Step 2: 执行查询
                query = """
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
                """
                
                all_table_rows = self.db_manager.execute_query(query)
                
                if not all_table_rows:
                    self.logger.warning("数据库中未找到任何用户表")
                    return []

                # 修正：db_manager.execute_query 返回的是字典列表, e.g. [{'name': 'table1'}, ...]
                all_table_names = [row['name'] for row in all_table_rows]

                # 如果提供了表类型，则进行过滤
                if table_type:
                    prefix = f"{table_type}_"
                    filtered_names = [name for name in all_table_names if name.startswith(prefix)]
                    
                    # Step 3: 检查结果一致性
                    if attempt == 0 and len(filtered_names) < self._get_expected_table_count(table_type):
                        self.logger.warning(f"首次查询发现表数量异常: {len(filtered_names)} 个表，可能需要重试")
                        if attempt < max_retries - 1:
                            import time
                            time.sleep(retry_delay)
                            continue
                    
                    self.logger.info(f"找到 {len(filtered_names)} 个匹配类型 '{table_type}' 的表")
                else:
                    filtered_names = all_table_names
                    self.logger.info(f"找到 {len(filtered_names)} 个总表")

                # 为了兼容性，以相同的字典格式返回。
                # 现在从表名中解析年份、月份和显示名称信息
                table_list = []
                
                # 定义员工类型映射
                employee_type_map = {
                    'retired_employees': '离休人员',
                    'pension_employees': '退休人员', 
                    'active_employees': '全部在职人员',
                    'a_grade_employees': 'A岗职工'
                }
                
                for name in filtered_names:
                    table_info = {
                        'table_name': name,
                        'description': '',  # 元数据从此直接查询中不可用
                        'version': 1,       # 假定版本为1
                        'schema_hash': ''
                    }
                    
                    # 如果是salary_data类型的表，解析年份、月份和显示名称
                    if table_type == 'salary_data' and name.startswith('salary_data_'):
                        parsed = self._parse_table_name(name)
                        if parsed:
                            year, month, employee_type = parsed
                            table_info['year'] = year
                            table_info['month'] = month
                            table_info['employee_type'] = employee_type
                            
                            # 生成友好的显示名称
                            display_name = employee_type_map.get(employee_type, employee_type)
                            table_info['display_name'] = display_name
                            table_info['description'] = f"{year}年{month:02d}月{display_name}工资数据"
                    
                    table_list.append(table_info)

                return table_list

            except Exception as e:
                self.logger.error(f"获取表列表失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    import time
                    time.sleep(retry_delay * (attempt + 1))  # 递增延迟
                    continue
                else:
                    self.logger.error("获取表列表最终失败", exc_info=True)
                    return []

    def _ensure_database_sync(self):
        """确保WAL模式数据完全同步"""
        try:
            # 执行WAL checkpoint确保数据完全写入主数据库
            checkpoint_query = "PRAGMA wal_checkpoint(FULL)"
            result = self.db_manager.execute_query(checkpoint_query)
            
            if result:
                self.logger.debug(f"WAL checkpoint执行完成: {result}")
            
            # 验证数据库连接状态
            integrity_check = "PRAGMA quick_check"
            integrity_result = self.db_manager.execute_query(integrity_check)
            
            if integrity_result and len(integrity_result) > 0:
                check_result = integrity_result[0].get('quick_check', 'unknown')
                if check_result != 'ok':
                    self.logger.warning(f"数据库完整性检查异常: {check_result}")
                    
        except Exception as e:
            self.logger.warning(f"数据库同步检查失败: {e}")

    def _get_expected_table_count(self, table_type: str) -> int:
        """获取特定表类型的预期表数量（用于异常检测）"""
        if table_type == 'salary_data':
            # 降低期望表数量，避免在数据库初始状态时触发异常检测
            # 如果数据库确实为空，应该允许正常启动
            return 1  # 至少有1个表时才认为是正常状态，0个表可能是初始状态
        return 0  # 其他表类型暂不检查

    def save_dataframe_to_table(self, df: pd.DataFrame, table_name: str) -> Tuple[bool, str]:
        """
        将DataFrame数据保存到数据库的指定表中 (覆盖写入)
        此方法是事务性的，并包含完整的错误处理。
        Returns:
            Tuple[bool, str]: A tuple containing a boolean success flag 
                              and a message string.
        """
        if df is None or df.empty:
            self.logger.warning("输入数据为空，无法保存。")
            return False, "输入数据为空，无法保存。"

        try:
            # Step 1: Prepare DataFrame
            df_copy = df.copy()
            
            # 1a. 标准化字段映射
            field_mappings = {
                # 员工ID字段映射
                "工号": "employee_id", 
                "职工编号": "employee_id", 
                "编号": "employee_id", 
                "人员代码": "employee_id",
                
                # 员工姓名字段映射
                "姓名": "employee_name", 
                "职工姓名": "employee_name", 
                "员工姓名": "employee_name", 
                "名字": "employee_name",
                
                # 其他可能的字段映射
                "身份证": "id_card",
                "身份证号": "id_card",
                "身份证号码": "id_card",
                "部门": "department",
                "职位": "position",
                "岗位": "position",
                "基本工资": "basic_salary",
                "基础工资": "basic_salary",
                "绩效": "performance_bonus",
                "绩效奖金": "performance_bonus",
                "加班费": "overtime_pay",
                "津贴": "allowance",
                "补贴": "allowance",
                "扣款": "deduction",
                "扣除": "deduction",
                "应发工资": "total_salary",
                "实发工资": "total_salary",
                "工资合计": "total_salary"
            }
            
            active_map = {k: v for k, v in field_mappings.items() if k in df_copy.columns}
            
            if active_map:
                df_copy.rename(columns=active_map, inplace=True)
                self.logger.info(f"列名映射成功: {list(active_map.keys())} -> {list(active_map.values())}")
            
            # 检查必须字段
            if 'employee_id' not in df_copy.columns:
                raise ValueError("数据中缺少必须的员工ID列 (工号/职工编号/编号/人员代码)")

            # 1b. Clean all other column names
            final_column_map = {col: self._clean_column_name(col) for col in df_copy.columns}
            df_copy.rename(columns=final_column_map, inplace=True)
            prepared_df = df_copy

            # Step 2: Ensure table exists
            if not self.db_manager.table_exists(table_name):
                self.logger.info(f"表 {table_name} 不存在，将根据模板创建...")
                
                # 改进的模板检测逻辑
                template_key = None
                if "salary_data" in table_name:
                    # 所有salary_data相关的表都使用salary_data模板
                    template_key = "salary_data"
                elif "misc_data" in table_name:
                    template_key = "misc_data"
                elif "salary_changes" in table_name:
                    template_key = "salary_changes"
                else:
                    # 如果表名不匹配任何已知模板，尝试根据表名前缀推断
                    table_prefix = table_name.split('_')[0]
                    if table_prefix in self._table_templates:
                        template_key = table_prefix
                    else:
                        # 默认使用salary_data模板作为兜底
                        template_key = "salary_data"
                        self.logger.warning(f"表 {table_name} 无法匹配具体模板，使用默认salary_data模板")
                
                if not template_key:
                    raise ValueError(f"无法为表 {table_name} 找到合适的创建模板。")
                
                base_schema = self._table_templates[template_key]
                # 创建基本表结构，不需要动态扩展
                final_schema = TableSchema(
                    table_name=table_name,
                    columns=base_schema.columns,
                    primary_key=base_schema.primary_key,
                    indexes=base_schema.indexes,
                    description=f"动态创建的表: {table_name}"
                )
                
                if not self._create_table_from_schema(final_schema):
                    raise RuntimeError(f"创建表 {table_name} 失败。")

            # Step 3: Insert data
            table_info = self.db_manager.get_table_info(table_name)
            final_columns = {col['name'] for col in table_info}
            
            # 自动填充必须字段
            current_time = datetime.now().isoformat()
            
            # 从表名中提取年月信息（格式：salary_data_2025_05_xxxx）
            table_parts = table_name.split('_')
            if len(table_parts) >= 4 and table_parts[0] == 'salary' and table_parts[1] == 'data':
                try:
                    year_from_table = int(table_parts[2])
                    month_from_table = table_parts[3]
                    # 格式化月份为 YYYY-MM 格式
                    if len(month_from_table) == 2:
                        month_str = f"{year_from_table}-{month_from_table}"
                    else:
                        month_str = f"{year_from_table}-{month_from_table.zfill(2)}"
                except (ValueError, IndexError):
                    # 如果无法从表名解析，使用当前时间
                    now = datetime.now()
                    year_from_table = now.year
                    month_str = now.strftime("%Y-%m")
            else:
                # 非标准表名，使用当前时间
                now = datetime.now()
                year_from_table = now.year
                month_str = now.strftime("%Y-%m")
            
            # 添加必须字段的默认值
            for col_name in final_columns:
                if col_name not in prepared_df.columns:
                    if col_name == 'month':
                        prepared_df[col_name] = month_str
                    elif col_name == 'year':
                        prepared_df[col_name] = year_from_table
                    elif col_name == 'created_at':
                        prepared_df[col_name] = current_time
                    elif col_name == 'updated_at':
                        prepared_df[col_name] = current_time
                    elif col_name == 'id':
                        # id 是自增字段，不需要手动设置
                        continue
                    else:
                        # 对于其他可能的必须字段，设置默认值
                        prepared_df[col_name] = None
            
            df_to_insert = prepared_df[[col for col in prepared_df.columns if col in final_columns]]
            
            # 清空表
            self.db_manager.execute_update(f"DELETE FROM {self._quote_identifier(table_name)}")

            # 批量插入数据
            columns = df_to_insert.columns.tolist()
            quoted_columns = [self._quote_identifier(col) for col in columns]
            placeholders = ', '.join(['?'] * len(columns))
            sql = f"INSERT INTO {self._quote_identifier(table_name)} ({', '.join(quoted_columns)}) VALUES ({placeholders})"
            
            data = [tuple(row) for row in df_to_insert.itertuples(index=False, name=None)]
            self.db_manager.execute_batch(sql, data)
            
            self.logger.info(f"成功向表 {table_name} 保存 {len(data)} 条数据。")
            return True, f"成功向表 {table_name} 保存 {len(data)} 条数据。"
            
        except Exception as e:
            self.logger.error(f"保存数据到表 {table_name} 失败: {e}", exc_info=True)
            return False, f"保存数据到表 {table_name} 失败: {e}"

    @staticmethod
    def _clean_column_name(col_name: str) -> str:
        """清理列名，使其符合SQL规范。"""
        if not col_name:
            return "unknown_column"
        
        # 保持原始列名，只进行必要的清理
        # 移除前后空格
        cleaned = str(col_name).strip()
        
        # 如果是空字符串，返回默认名称
        if not cleaned:
            return "unknown_column"
        
        return cleaned

    def get_dataframe_from_table(self, table_name: str) -> Optional[pd.DataFrame]:
        """
        从指定表中获取所有数据并返回一个pandas DataFrame

        Args:
            table_name (str): 表名

        Returns:
            Optional[pd.DataFrame]: 包含表数据的DataFrame，如果表不存在或查询出错则返回None
        """
        self.logger.info(f"正在从表 {table_name} 获取数据...")
        if not self.table_exists(table_name):
            self.logger.warning(f"表 {table_name} 不存在，无法获取数据。")
            return None
        
        try:
            query = f'SELECT * FROM "{table_name}"'
            data = self.db_manager.execute_query(query)
            
            if data:
                df = pd.DataFrame(data)
                self.logger.info(f"成功从表 {table_name} 获取 {len(df)} 行数据。")
                return df
            else:
                self.logger.info(f"表 {table_name} 为空。")
                return pd.DataFrame()
        except Exception as e:
            self.logger.error(f"从表 {table_name} 获取数据时出错: {e}")
            return None

    def get_dataframe_paginated(self, table_name: str, page: int = 1, page_size: int = 100) -> Tuple[Optional[pd.DataFrame], int]:
        """
        分页查询表数据
        
        Args:
            table_name (str): 表名
            page (int): 页码(从1开始)
            page_size (int): 每页记录数
            
        Returns:
            Tuple[Optional[pd.DataFrame], int]: (页面数据, 总记录数)
        """
        self.logger.info(f"正在从表 {table_name} 分页获取数据: 第{page}页, 每页{page_size}条")
        
        if not self.table_exists(table_name):
            self.logger.warning(f"表 {table_name} 不存在，无法获取数据。")
            return None, 0
        
        try:
            # 获取总记录数
            total_count = self.get_table_record_count(table_name)
            
            if total_count == 0:
                self.logger.info(f"表 {table_name} 为空。")
                return pd.DataFrame(), 0
            
            # 计算偏移量
            offset = (page - 1) * page_size
            
            # 构建分页查询SQL
            query = f'SELECT * FROM "{table_name}" LIMIT {page_size} OFFSET {offset}'
            data = self.db_manager.execute_query(query)
            
            if data:
                df = pd.DataFrame(data)
                self.logger.info(f"成功从表 {table_name} 获取第{page}页数据: {len(df)} 行，总计{total_count}行")
                return df, total_count
            else:
                self.logger.info(f"表 {table_name} 第{page}页无数据。")
                return pd.DataFrame(), total_count
                
        except Exception as e:
            self.logger.error(f"从表 {table_name} 分页获取数据时出错: {e}", exc_info=True)
            return None, 0

    def get_table_record_count(self, table_name: str) -> int:
        """
        获取表总记录数
        
        Args:
            table_name (str): 表名
            
        Returns:
            int: 表的总记录数，出错时返回0
        """
        if not self.table_exists(table_name):
            self.logger.warning(f"表 {table_name} 不存在，无法获取记录数。")
            return 0
        
        try:
            query = f'SELECT COUNT(*) FROM "{table_name}"'
            result = self.db_manager.execute_query(query)
            
            if result and len(result) > 0:
                # 修复：正确访问字典格式的查询结果
                count_result = result[0]
                if isinstance(count_result, dict):
                    # 获取COUNT(*)的值
                    count = count_result.get('COUNT(*)', 0)
                else:
                    # 兼容可能的其他格式
                    count = count_result[0] if hasattr(count_result, '__getitem__') else 0
                
                self.logger.debug(f"表 {table_name} 总记录数: {count}")
                return count
            else:
                return 0
                
        except Exception as e:
            self.logger.error(f"获取表 {table_name} 记录数时出错: {e}", exc_info=True)
            return 0

    def get_table_preview(self, table_name: str, preview_size: int = 10) -> Optional[pd.DataFrame]:
        """
        获取表数据预览(用于快速显示表结构和示例数据)
        
        Args:
            table_name (str): 表名
            preview_size (int): 预览行数，默认10行
            
        Returns:
            Optional[pd.DataFrame]: 预览数据，出错时返回None
        """
        self.logger.info(f"正在获取表 {table_name} 的预览数据: {preview_size} 行")
        
        if not self.table_exists(table_name):
            self.logger.warning(f"表 {table_name} 不存在，无法获取预览数据。")
            return None
        
        try:
            query = f'SELECT * FROM "{table_name}" LIMIT {preview_size}'
            data = self.db_manager.execute_query(query)
            
            if data:
                df = pd.DataFrame(data)
                self.logger.info(f"成功获取表 {table_name} 预览数据: {len(df)} 行")
                return df
            else:
                self.logger.info(f"表 {table_name} 为空，返回空预览。")
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"获取表 {table_name} 预览数据时出错: {e}", exc_info=True)
            return None

    def get_navigation_tree_data(self) -> Dict[int, Dict[int, List[Dict[str, str]]]]:
        """
        从元数据获取用于构建导航树的结构化数据

        Returns:
            Dict[int, Dict[int, List[Dict[str, str]]]]: 
            一个嵌套字典，结构为 {year: {month: [item_info, ...]}}
            item_info 包含: display_name, table_name, icon
        """
        try:
            metadata = self.get_table_list(table_type='salary_data')
            if not metadata:
                self.logger.warning("在 table_metadata 中未找到任何 'salary_data' 类型的表")
                return {}

            tree_data = {}
            
            # 定义图标映射
            icon_map = {
                '全部在职人员': '👥',
                '离休人员': '🏖️',
                '退休人员': '🏠',
                'A岗职工': '🏢'
            }

            for item in metadata:
                year = item.get('year')
                month = item.get('month')
                
                if not year or not month:
                    self.logger.warning(f"元数据项 {item.get('table_name')} 缺少年份或月份信息，已跳过")
                    continue

                # 确保年份和月份是整数
                try:
                    year = int(year)
                    month = int(month)
                except (ValueError, TypeError):
                    self.logger.warning(f"元数据项 {item.get('table_name')} 的年份或月份格式无效，已跳过")
                    continue

                if year not in tree_data:
                    tree_data[year] = {}
                if month not in tree_data[year]:
                    tree_data[year][month] = []
                
                display_name = item.get('display_name', item.get('table_name'))
                
                tree_data[year][month].append({
                    'display_name': display_name,
                    'table_name': item.get('table_name'),
                    'icon': icon_map.get(display_name, '📄') # 兜底图标
                })
            
            # 按年份和月份排序
            sorted_tree_data = {y: dict(sorted(tree_data[y].items(), key=lambda m: m[0], reverse=True)) 
                                for y in sorted(tree_data.keys(), reverse=True)}

            return sorted_tree_data

        except Exception as e:
            self.logger.error(f"获取导航树数据失败: {e}", exc_info=True)
            return {}

    def _parse_table_name(self, table_name: str) -> Optional[Tuple[int, int, str]]:
        """
        解析表名获取年月和员工类型

        Args:
            table_name (str): 表名 (例如: salary_data_2025_02_retired_employees)

        Returns:
            Optional[Tuple[int, int, str]]: 返回年份、月份和员工类型，如果解析失败则返回None
        """
        try:
            parts = table_name.split('_')
            if len(parts) >= 5 and parts[0] == 'salary' and parts[1] == 'data':
                year = int(parts[2])
                month = int(parts[3])
                # 员工类型可能包含多个下划线，如retired_employees
                employee_type = '_'.join(parts[4:])
                return year, month, employee_type
            else:
                return None
        except (ValueError, IndexError):
            return None